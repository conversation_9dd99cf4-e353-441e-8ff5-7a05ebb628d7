"use client";

import { gsap } from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef } from "react";

function AuthPage() {
  // Refs for GSAP animations
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const illustrationRef = useRef<HTMLDivElement>(null);

  // Animation effects
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial state - hide all elements
      gsap.set([titleRef.current, descriptionRef.current, buttonsRef.current], {
        opacity: 0,
        y: 40,
      });

      gsap.set(illustrationRef.current, {
        opacity: 0,
        scale: 0.8,
        rotation: -5,
      });

      // Entrance animations with stagger
      const tl = gsap.timeline();

      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
      })
        .to(
          descriptionRef.current,
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
          },
          "-=0.5",
        )
        .to(
          buttonsRef.current,
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
          },
          "-=0.4",
        )
        .to(
          illustrationRef.current,
          {
            opacity: 1,
            scale: 1,
            rotation: 0,
            duration: 1,
            ease: "back.out(1.7)",
          },
          "-=0.6",
        );
    }, containerRef);

    return () => ctx.revert();
  }, []);

  // Handle button hover animations
  const handleButtonHover = (element: HTMLElement, isHovering: boolean) => {
    gsap.to(element, {
      scale: isHovering ? 1.05 : 1,
      y: isHovering ? -3 : 0,
      duration: 0.3,
      ease: "power2.out",
    });
  };
  return (
    <div ref={containerRef} className="lg:grid lg:grid-cols-2 lg:gap-20 place-items-center my-5">
      <div className="space-y-10 w-full max-w-lg">
        <div ref={titleRef} className="space-y-3">
          <h2 className="text-3xl sm:text-5xl font-bold text-gray-800">
            Welcome to <span className="text-primary">Sherif</span>
          </h2>
          <h2 className="text-primary text-3xl sm:text-5xl font-bold">Franca platfom</h2>
        </div>
        <div>
          <p ref={descriptionRef} className="text-[#ABADB7] font-medium">
            Discover fun and effective dance workouts for all levels. No experience needed – just
            move to the beat! Unleash your inner dancer and discover a fun way to achieve your
            fitness goals.
          </p>
        </div>
        <div
          ref={buttonsRef}
          className="flex flex-col sm:flex-row items-center gap-5 sm:gap-10 font-medium"
        >
          <Link
            href="/signin"
            className="text-white bg-primary w-full sm:w-48 py-3 rounded text-center block"
            onMouseEnter={(e) => handleButtonHover(e.currentTarget, true)}
            onMouseLeave={(e) => handleButtonHover(e.currentTarget, false)}
          >
            Have Account Sign in
          </Link>
          <Link
            href="/select-language"
            className="border border-primary text-primary w-full sm:w-48 py-3 rounded text-center block"
            onMouseEnter={(e) => handleButtonHover(e.currentTarget, true)}
            onMouseLeave={(e) => handleButtonHover(e.currentTarget, false)}
          >
            New User Sign Up
          </Link>
        </div>
      </div>
      <div ref={illustrationRef} className="hidden lg:flex lg:justify-center lg:items-center">
        <Image src="/auth.png" alt="auth" width={500} height={500} />
      </div>
    </div>
  );
}

export default AuthPage;
